using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using NJS.Application.Dtos;
using NJS.Domain.Enums;
using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Xunit;

namespace NJS.API.Tests
{
    public class FrequencyApiTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public FrequencyApiTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task CreateDailyPlannedHours_ShouldReturnCreated()
        {
            // Arrange
            var request = new FrequencyRequestDto
            {
                WBSTaskId = 1,
                FrequencyType = FrequencyType.Day,
                Hours = 8.0,
                Date = DateTime.Today
            };

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PostAsync("/api/frequency/Day", content);

            // Assert
            Assert.True(response.IsSuccessStatusCode || response.StatusCode == System.Net.HttpStatusCode.Conflict);
            // Note: Conflict is acceptable if the record already exists
        }

        [Fact]
        public async Task CreateWeeklyPlannedHours_ShouldReturnCreated()
        {
            // Arrange
            var weekStart = GetMondayOfCurrentWeek();
            var request = new FrequencyRequestDto
            {
                WBSTaskId = 1,
                FrequencyType = FrequencyType.Week,
                Hours = 40.0,
                WeekStartDate = weekStart,
                WeekEndDate = weekStart.AddDays(6)
            };

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PostAsync("/api/frequency/Week", content);

            // Assert
            Assert.True(response.IsSuccessStatusCode || response.StatusCode == System.Net.HttpStatusCode.Conflict);
        }

        [Fact]
        public async Task CreateMonthlyPlannedHours_ShouldReturnCreated()
        {
            // Arrange
            var currentDate = DateTime.Today;
            var request = new FrequencyRequestDto
            {
                WBSTaskId = 1,
                FrequencyType = FrequencyType.Month,
                Hours = 160.0,
                Year = currentDate.Year,
                Month = currentDate.Month,
                MonthName = currentDate.ToString("MMMM")
            };

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PostAsync("/api/frequency/Month", content);

            // Assert
            Assert.True(response.IsSuccessStatusCode || response.StatusCode == System.Net.HttpStatusCode.Conflict);
        }

        [Fact]
        public async Task GetPlannedHoursByWBSTask_ShouldReturnOk()
        {
            // Act
            var response = await _client.GetAsync("/api/frequency/Day?wbsTaskId=1");

            // Assert
            Assert.True(response.IsSuccessStatusCode);
        }

        [Fact]
        public async Task GetPlannedHoursByDateRange_ShouldReturnOk()
        {
            // Arrange
            var startDate = DateTime.Today.AddDays(-30);
            var endDate = DateTime.Today;

            // Act
            var response = await _client.GetAsync($"/api/frequency/Day/range?wbsTaskId=1&startDate={startDate:yyyy-MM-dd}&endDate={endDate:yyyy-MM-dd}");

            // Assert
            Assert.True(response.IsSuccessStatusCode);
        }

        private DateTime GetMondayOfCurrentWeek()
        {
            var today = DateTime.Today;
            var daysFromMonday = (int)today.DayOfWeek - (int)DayOfWeek.Monday;
            if (daysFromMonday < 0) daysFromMonday += 7;
            return today.AddDays(-daysFromMonday);
        }
    }
}
