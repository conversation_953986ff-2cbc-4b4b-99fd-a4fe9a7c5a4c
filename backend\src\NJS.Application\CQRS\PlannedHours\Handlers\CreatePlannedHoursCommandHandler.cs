using MediatR;
using Microsoft.Extensions.Logging;
using NJS.Application.CQRS.PlannedHours.Commands;
using NJS.Application.Dtos;
using NJS.Application.Services.IContract;
using NJS.Domain.Entities;
using NJS.Domain.Enums;
using NJS.Repositories.Interfaces;
using System;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;

namespace NJS.Application.CQRS.PlannedHours.Handlers
{
    public class CreatePlannedHoursCommandHandler : IRequestHandler<CreatePlannedHoursCommand, FrequencyResponseDto>
    {
        private readonly IPlannedHoursRepository _plannedHoursRepository;
        private readonly IPlannedHoursAggregationService _aggregationService;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<CreatePlannedHoursCommandHandler> _logger;

        public CreatePlannedHoursCommandHandler(
            IPlannedHoursRepository plannedHoursRepository,
            IPlannedHoursAggregationService aggregationService,
            ICurrentUserService currentUserService,
            ILogger<CreatePlannedHoursCommandHandler> logger)
        {
            _plannedHoursRepository = plannedHoursRepository ?? throw new ArgumentNullException(nameof(plannedHoursRepository));
            _aggregationService = aggregationService ?? throw new ArgumentNullException(nameof(aggregationService));
            _currentUserService = currentUserService ?? throw new ArgumentNullException(nameof(currentUserService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<FrequencyResponseDto> Handle(CreatePlannedHoursCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Creating planned hours for WBSTask {WBSTaskId}, frequency {FrequencyType}", 
                    request.Request.WBSTaskId, request.Request.FrequencyType);

                // Validate request
                ValidateRequest(request.Request);

                var currentUser = _currentUserService.UserId;
                var createdEntity = await CreateEntityByFrequencyType(request.Request, currentUser);

                // Convert to response DTO
                var response = MapToResponseDto(createdEntity, request.Request.FrequencyType);

                // Trigger auto-aggregation if it's a daily record
                if (request.Request.FrequencyType == FrequencyType.Day && request.Request.Date.HasValue)
                {
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await _aggregationService.AutoAggregateAfterDailyChangeAsync(
                                request.Request.WBSTaskId, 
                                request.Request.Date.Value, 
                                currentUser);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error during auto-aggregation after creating daily record");
                        }
                    }, cancellationToken);
                }
                // Trigger auto-aggregation if it's a weekly record
                else if (request.Request.FrequencyType == FrequencyType.Week && request.Request.WeekStartDate.HasValue)
                {
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await _aggregationService.AutoAggregateAfterWeeklyChangeAsync(
                                request.Request.WBSTaskId, 
                                request.Request.WeekStartDate.Value, 
                                currentUser);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error during auto-aggregation after creating weekly record");
                        }
                    }, cancellationToken);
                }

                _logger.LogInformation("Successfully created planned hours with ID {Id} for WBSTask {WBSTaskId}", 
                    response.Id, request.Request.WBSTaskId);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating planned hours for WBSTask {WBSTaskId}, frequency {FrequencyType}", 
                    request.Request.WBSTaskId, request.Request.FrequencyType);
                throw;
            }
        }

        private void ValidateRequest(FrequencyRequestDto request)
        {
            switch (request.FrequencyType)
            {
                case FrequencyType.Day:
                    if (!request.Date.HasValue)
                        throw new ValidationException("Date is required for daily frequency");
                    break;
                case FrequencyType.Week:
                    if (!request.WeekStartDate.HasValue || !request.WeekEndDate.HasValue)
                        throw new ValidationException("WeekStartDate and WeekEndDate are required for weekly frequency");
                    break;
                case FrequencyType.Month:
                    if (!request.Year.HasValue || !request.Month.HasValue)
                        throw new ValidationException("Year and Month are required for monthly frequency");
                    if (string.IsNullOrEmpty(request.MonthName))
                        request.MonthName = _aggregationService.GetMonthName(request.Month.Value);
                    break;
                default:
                    throw new ValidationException($"Invalid frequency type: {request.FrequencyType}");
            }
        }

        private async Task<object> CreateEntityByFrequencyType(FrequencyRequestDto request, string currentUser)
        {
            var now = DateTime.UtcNow;

            switch (request.FrequencyType)
            {
                case FrequencyType.Day:
                    // Check if daily record already exists
                    if (await _plannedHoursRepository.DayExistsAsync(request.WBSTaskId, request.Date.Value))
                        throw new InvalidOperationException($"Daily record already exists for WBSTask {request.WBSTaskId} on {request.Date.Value:yyyy-MM-dd}");

                    var dayEntity = new PlannedHoursDay
                    {
                        WBSTaskId = request.WBSTaskId,
                        Date = request.Date.Value.Date,
                        Hours = request.Hours,
                        CreatedAt = now,
                        CreatedBy = currentUser
                    };
                    dayEntity.Id = await _plannedHoursRepository.AddDayAsync(dayEntity);
                    return dayEntity;

                case FrequencyType.Week:
                    var weekStart = _aggregationService.GetWeekStartDate(request.WeekStartDate.Value);
                    var weekEnd = _aggregationService.GetWeekEndDate(request.WeekStartDate.Value);

                    // Check if weekly record already exists
                    if (await _plannedHoursRepository.WeekExistsAsync(request.WBSTaskId, weekStart))
                        throw new InvalidOperationException($"Weekly record already exists for WBSTask {request.WBSTaskId} starting {weekStart:yyyy-MM-dd}");

                    var weekEntity = new PlannedHoursWeek
                    {
                        WBSTaskId = request.WBSTaskId,
                        WeekStartDate = weekStart,
                        WeekEndDate = weekEnd,
                        Hours = request.Hours,
                        CreatedAt = now,
                        CreatedBy = currentUser
                    };
                    weekEntity.Id = await _plannedHoursRepository.AddWeekAsync(weekEntity);
                    return weekEntity;

                case FrequencyType.Month:
                    // Check if monthly record already exists
                    if (await _plannedHoursRepository.MonthExistsAsync(request.WBSTaskId, request.Year.Value, request.Month.Value))
                        throw new InvalidOperationException($"Monthly record already exists for WBSTask {request.WBSTaskId} for {request.Year.Value}-{request.Month.Value:D2}");

                    var monthEntity = new PlannedHoursMonth
                    {
                        WBSTaskId = request.WBSTaskId,
                        Year = request.Year.Value,
                        Month = request.Month.Value,
                        MonthName = request.MonthName,
                        Hours = request.Hours,
                        CreatedAt = now,
                        CreatedBy = currentUser
                    };
                    monthEntity.Id = await _plannedHoursRepository.AddMonthAsync(monthEntity);
                    return monthEntity;

                default:
                    throw new ArgumentException($"Invalid frequency type: {request.FrequencyType}");
            }
        }

        private FrequencyResponseDto MapToResponseDto(object entity, FrequencyType frequencyType)
        {
            return frequencyType switch
            {
                FrequencyType.Day => MapDayToDto((PlannedHoursDay)entity),
                FrequencyType.Week => MapWeekToDto((PlannedHoursWeek)entity),
                FrequencyType.Month => MapMonthToDto((PlannedHoursMonth)entity),
                _ => throw new ArgumentException($"Invalid frequency type: {frequencyType}")
            };
        }

        private FrequencyResponseDto MapDayToDto(PlannedHoursDay day)
        {
            return new FrequencyResponseDto
            {
                Id = day.Id,
                WBSTaskId = day.WBSTaskId,
                FrequencyType = FrequencyType.Day,
                Hours = day.Hours,
                IsAggregated = day.IsAggregated,
                CreatedAt = day.CreatedAt,
                CreatedBy = day.CreatedBy,
                UpdatedAt = day.UpdatedAt,
                UpdatedBy = day.UpdatedBy,
                Date = day.Date
            };
        }

        private FrequencyResponseDto MapWeekToDto(PlannedHoursWeek week)
        {
            return new FrequencyResponseDto
            {
                Id = week.Id,
                WBSTaskId = week.WBSTaskId,
                FrequencyType = FrequencyType.Week,
                Hours = week.Hours,
                IsAggregated = week.IsAggregated,
                CreatedAt = week.CreatedAt,
                CreatedBy = week.CreatedBy,
                UpdatedAt = week.UpdatedAt,
                UpdatedBy = week.UpdatedBy,
                WeekStartDate = week.WeekStartDate,
                WeekEndDate = week.WeekEndDate
            };
        }

        private FrequencyResponseDto MapMonthToDto(PlannedHoursMonth month)
        {
            return new FrequencyResponseDto
            {
                Id = month.Id,
                WBSTaskId = month.WBSTaskId,
                FrequencyType = FrequencyType.Month,
                Hours = month.Hours,
                IsAggregated = month.IsAggregated,
                CreatedAt = month.CreatedAt,
                CreatedBy = month.CreatedBy,
                UpdatedAt = month.UpdatedAt,
                UpdatedBy = month.UpdatedBy,
                Year = month.Year,
                Month = month.Month,
                MonthName = month.MonthName
            };
        }
    }
}
