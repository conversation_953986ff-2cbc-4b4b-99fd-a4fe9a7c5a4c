using MediatR;
using Microsoft.Extensions.Logging;
using NJS.Application.CQRS.PlannedHours.Commands;
using NJS.Application.Services.IContract;
using NJS.Domain.Enums;
using NJS.Repositories.Interfaces;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace NJS.Application.CQRS.PlannedHours.Handlers
{
    public class DeletePlannedHoursCommandHandler : IRequestHandler<DeletePlannedHoursCommand, bool>
    {
        private readonly IPlannedHoursRepository _plannedHoursRepository;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<DeletePlannedHoursCommandHandler> _logger;

        public DeletePlannedHoursCommandHandler(
            IPlannedHoursRepository plannedHoursRepository,
            ICurrentUserService currentUserService,
            ILogger<DeletePlannedHoursCommandHandler> logger)
        {
            _plannedHoursRepository = plannedHoursRepository ?? throw new ArgumentNullException(nameof(plannedHoursRepository));
            _currentUserService = currentUserService ?? throw new ArgumentNullException(nameof(currentUserService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<bool> Handle(DeletePlannedHoursCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Deleting planned hours with ID {Id}, frequency {FrequencyType}", 
                    request.Id, request.FrequencyType);

                // Check if the record exists and validate deletion rules
                await ValidateDeleteOperation(request.Id, request.FrequencyType);

                // Perform the deletion
                await _plannedHoursRepository.DeleteAsync(request.FrequencyType, request.Id);

                _logger.LogInformation("Successfully deleted planned hours with ID {Id}, frequency {FrequencyType}", 
                    request.Id, request.FrequencyType);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting planned hours with ID {Id}, frequency {FrequencyType}", 
                    request.Id, request.FrequencyType);
                throw;
            }
        }

        private async Task ValidateDeleteOperation(int id, FrequencyType frequencyType)
        {
            switch (frequencyType)
            {
                case FrequencyType.Day:
                    var dayEntity = await _plannedHoursRepository.GetDayByIdAsync(id);
                    if (dayEntity == null)
                        throw new InvalidOperationException($"Daily planned hours record with ID {id} not found");

                    // Prevent deleting aggregated records
                    if (dayEntity.IsAggregated)
                        throw new InvalidOperationException("Cannot delete aggregated daily records. Delete the source weekly record instead.");
                    break;

                case FrequencyType.Week:
                    var weekEntity = await _plannedHoursRepository.GetWeekByIdAsync(id);
                    if (weekEntity == null)
                        throw new InvalidOperationException($"Weekly planned hours record with ID {id} not found");

                    // Prevent deleting aggregated records
                    if (weekEntity.IsAggregated)
                        throw new InvalidOperationException("Cannot delete aggregated weekly records. Delete the source monthly record instead.");

                    // Check if this weekly record has aggregated daily records
                    if (weekEntity.DailyRecords != null && weekEntity.DailyRecords.Count > 0)
                    {
                        _logger.LogInformation("Deleting weekly record {Id} will also remove {Count} aggregated daily records", 
                            id, weekEntity.DailyRecords.Count);
                        
                        // The database cascade rules will handle the deletion of related daily records
                        // But we need to be aware that this will happen
                    }
                    break;

                case FrequencyType.Month:
                    var monthEntity = await _plannedHoursRepository.GetMonthByIdAsync(id);
                    if (monthEntity == null)
                        throw new InvalidOperationException($"Monthly planned hours record with ID {id} not found");

                    // Check if this monthly record has aggregated weekly records
                    if (monthEntity.WeeklyRecords != null && monthEntity.WeeklyRecords.Count > 0)
                    {
                        _logger.LogInformation("Deleting monthly record {Id} will also remove {Count} aggregated weekly records", 
                            id, monthEntity.WeeklyRecords.Count);
                        
                        // The database cascade rules will handle the deletion of related weekly records
                        // But we need to be aware that this will happen
                    }
                    break;

                default:
                    throw new ArgumentException($"Invalid frequency type: {frequencyType}");
            }
        }
    }
}
