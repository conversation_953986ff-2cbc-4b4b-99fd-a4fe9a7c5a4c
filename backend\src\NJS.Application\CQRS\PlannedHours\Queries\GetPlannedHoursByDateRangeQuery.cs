using MediatR;
using NJS.Application.Dtos;
using NJS.Domain.Enums;
using System;
using System.Collections.Generic;

namespace NJS.Application.CQRS.PlannedHours.Queries
{
    public record GetPlannedHoursByDateRangeQuery : IRequest<IEnumerable<FrequencyResponseDto>>
    {
        public FrequencyType FrequencyType { get; init; }
        public int WBSTaskId { get; init; }
        public DateTime StartDate { get; init; }
        public DateTime EndDate { get; init; }

        public GetPlannedHoursByDateRangeQuery(FrequencyType frequencyType, int wbsTaskId, DateTime startDate, DateTime endDate)
        {
            FrequencyType = frequencyType;
            WBSTaskId = wbsTaskId;
            StartDate = startDate;
            EndDate = endDate;
        }
    }
}
