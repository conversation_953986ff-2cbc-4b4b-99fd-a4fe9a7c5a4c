using System;
using System.ComponentModel.DataAnnotations;
using NJS.Domain.Enums;

namespace NJS.Application.Dtos
{
    public class FrequencyRequestDto
    {
        [Required]
        public int WBSTaskId { get; set; }

        [Required]
        public FrequencyType FrequencyType { get; set; }

        [Required]
        [Range(0, 744, ErrorMessage = "Hours must be between 0 and 744")]
        public double Hours { get; set; }

        // For Day frequency
        public DateTime? Date { get; set; }

        // For Week frequency
        public DateTime? WeekStartDate { get; set; }
        public DateTime? WeekEndDate { get; set; }

        // For Month frequency
        public int? Year { get; set; }
        public int? Month { get; set; }
        public string MonthName { get; set; }
    }

    public class FrequencyResponseDto
    {
        public int Id { get; set; }
        public int WBSTaskId { get; set; }
        public FrequencyType FrequencyType { get; set; }
        public double Hours { get; set; }
        public bool IsAggregated { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; }

        // Frequency-specific data
        public DateTime? Date { get; set; }
        public DateTime? WeekStartDate { get; set; }
        public DateTime? WeekEndDate { get; set; }
        public int? Year { get; set; }
        public int? Month { get; set; }
        public string MonthName { get; set; }
    }
}
