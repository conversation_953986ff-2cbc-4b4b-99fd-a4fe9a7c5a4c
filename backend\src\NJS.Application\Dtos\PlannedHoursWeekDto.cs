using System;
using System.ComponentModel.DataAnnotations;

namespace NJS.Application.Dtos
{
    public class PlannedHoursWeekDto
    {
        public int Id { get; set; }

        [Required]
        public int WBSTaskId { get; set; }

        [Required]
        public DateTime WeekStartDate { get; set; }

        [Required]
        public DateTime WeekEndDate { get; set; }

        [Required]
        [Range(0, 168, ErrorMessage = "Weekly hours must be between 0 and 168")]
        public double Hours { get; set; }

        public bool IsAggregated { get; set; } = false;

        public int? AggregatedFromMonthId { get; set; }

        public DateTime CreatedAt { get; set; }
        public string? CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string? UpdatedBy { get; set; }
    }
}
