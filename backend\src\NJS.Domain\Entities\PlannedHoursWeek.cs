using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NJS.Domain.Entities
{
    public class PlannedHoursWeek
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int WBSTaskId { get; set; }

        [Required]
        public DateTime WeekStartDate { get; set; } // Monday of the week

        [Required]
        public DateTime WeekEndDate { get; set; } // Sunday of the week

        [Required]
        [Range(0, 168, ErrorMessage = "Weekly hours must be between 0 and 168 (24*7)")]
        public double Hours { get; set; }

        public bool IsAggregated { get; set; } = false; // Tracks if this was auto-created from daily aggregation

        public int? AggregatedFromMonthId { get; set; } // Reference to month record if this was disaggregated

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [StringLength(100)]
        public string CreatedBy { get; set; }

        public DateTime? UpdatedAt { get; set; }

        [StringLength(100)]
        public string UpdatedBy { get; set; }

        // Navigation properties
        [ForeignKey(nameof(WBSTaskId))]
        public WBSTask WBSTask { get; set; }

        [ForeignKey(nameof(AggregatedFromMonthId))]
        public PlannedHoursMonth AggregatedFromMonth { get; set; }

        // Collection of daily records that make up this week (if aggregated)
        public ICollection<PlannedHoursDay> DailyRecords { get; set; } = new List<PlannedHoursDay>();
    }
}
